#!/usr/bin/env node
/**
 * DevWorkbench Enhanced Web Server - 增强版Web界面
 * 提供完整的文件管理、项目控制和n8n集成功能
 */

import 'reflect-metadata';
import express from 'express';
import * as path from 'path';
import * as http from 'http';
import { Container } from 'inversify';
import { EventBus } from '../src/main/core/event-bus';
import { SimpleLogger } from '../src/main/services/logger-simple.service';
import { SimpleStateRepository } from '../src/main/services/state-simple.service';
import { ProjectDiscoveryService } from '../src/main/services/project-discovery.service';
import { WorkflowService } from '../src/main/services/workflow.service';
import { ServiceOrchestrator } from '../src/main/services/service-orchestrator.service';
import { HealthCheckService } from '../src/main/services/health-check.service';
import { BatchOperationsService } from '../src/main/services/batch-operations.service';
import { FileManagerService } from '../src/main/services/file-manager.service';
import { ConfigService } from '../src/main/services/config.service';
import { GitAdapter } from '../src/main/plugins/git/git-adapter';
import { ProcessLauncher } from '../src/main/services/process-launcher.service';
import { PortMonitor } from '../src/main/services/port-monitor.service';
import { TYPES, IProject, IWorkflowGroup } from '../src/shared/types';

// 任务运行器相关接口
interface IExecutableTask {
  id: string;
  name: string;          // 例如: "启动 N8N"
  command: string;       // 例如: "n8n start"
  defaultCwd?: string;   // 默认的工作目录 (可选)
  projectId?: string;    // 关联的项目ID (可选)
  description?: string;  // 任务描述
  category?: string;     // 任务分类 (development, build, test, deploy等)
  icon?: string;         // 任务图标
  createdAt: Date;
  updatedAt: Date;
}

interface ProcessHandle {
  id: string;
  taskId: string;
  taskName: string;
  command: string;
  cwd: string;
  status: 'running' | 'stopped' | 'error' | 'completed';
  startedAt: Date;
  endedAt?: Date;
  pid?: number;
  output: string[];      // 存储输出日志
}

class DevWorkbenchEnhancedWebServer {
  private container: Container;
  private projectService!: ProjectDiscoveryService;
  private workflowService!: WorkflowService;
  private orchestrator!: ServiceOrchestrator;
  private batchOps!: BatchOperationsService;
  private fileManager!: FileManagerService;
  private configService!: ConfigService;
  private app!: express.Application;
  private server!: http.Server;
  private port: number = 3333;

  // 任务运行器相关
  private tasks: Map<string, IExecutableTask> = new Map();
  private runningProcesses: Map<string, ProcessHandle> = new Map();

  constructor() {
    this.container = new Container();
    this.setupContainer();
    this.initializeServices();
    this.loadTasks();
    this.setupExpress();
    this.setupRoutes();
  }

  private setupContainer() {
    // 注册所有服务
    this.container.bind(TYPES.EventBus).to(EventBus).inSingletonScope();
    this.container.bind(TYPES.Logger).to(SimpleLogger).inSingletonScope();
    this.container.bind(TYPES.StateRepository).to(SimpleStateRepository).inSingletonScope();
    this.container.bind(TYPES.VersionControlAdapter).to(GitAdapter).inSingletonScope();
    this.container.bind(TYPES.ProjectDiscoveryService).to(ProjectDiscoveryService).inSingletonScope();
    this.container.bind(TYPES.ProcessLauncher).to(ProcessLauncher).inSingletonScope();
    this.container.bind(TYPES.PortMonitor).to(PortMonitor).inSingletonScope();
    this.container.bind(TYPES.WorkflowService).to(WorkflowService).inSingletonScope();
    this.container.bind(TYPES.ServiceOrchestrator).to(ServiceOrchestrator).inSingletonScope();
    this.container.bind(TYPES.HealthCheckService).to(HealthCheckService).inSingletonScope();
    this.container.bind(TYPES.BatchOperationsService).to(BatchOperationsService).inSingletonScope();
    this.container.bind('FileManagerService').to(FileManagerService).inSingletonScope();
    this.container.bind(TYPES.ConfigService).to(ConfigService).inSingletonScope();
  }

  private initializeServices() {
    this.projectService = this.container.get<ProjectDiscoveryService>(TYPES.ProjectDiscoveryService);
    this.workflowService = this.container.get<WorkflowService>(TYPES.WorkflowService);
    this.orchestrator = this.container.get<ServiceOrchestrator>(TYPES.ServiceOrchestrator);
    this.batchOps = this.container.get<BatchOperationsService>(TYPES.BatchOperationsService);
    this.fileManager = this.container.get<FileManagerService>('FileManagerService');
    this.configService = this.container.get<ConfigService>(TYPES.ConfigService);
  }

  // 任务管理方法
  private async loadTasks(): Promise<void> {
    try {
      const stateRepo = this.container.get<SimpleStateRepository>(TYPES.StateRepository);
      const storedTasks = await stateRepo.get<IExecutableTask[]>('executableTasks') || [];

      // 如果没有任务，创建一些默认任务
      if (storedTasks.length === 0) {
        const defaultTasks: IExecutableTask[] = [
          {
            id: 'task-npm-start',
            name: '启动开发服务器',
            command: 'npm start',
            description: '启动项目的开发服务器',
            category: 'development',
            icon: '🚀',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'task-npm-build',
            name: '构建项目',
            command: 'npm run build',
            description: '构建生产版本',
            category: 'build',
            icon: '🔨',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'task-npm-test',
            name: '运行测试',
            command: 'npm test',
            description: '运行项目测试',
            category: 'test',
            icon: '🧪',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'task-git-status',
            name: 'Git状态',
            command: 'git status',
            description: '查看Git仓库状态',
            category: 'git',
            icon: '📊',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'task-n8n-start',
            name: '启动N8N',
            command: 'n8n start',
            description: '启动N8N工作流引擎',
            category: 'automation',
            icon: '🔄',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];

        await this.saveTasks(defaultTasks);
        storedTasks.push(...defaultTasks);
      }

      storedTasks.forEach(task => this.tasks.set(task.id, task));
      console.log(`✅ 加载了 ${this.tasks.size} 个可执行任务`);
    } catch (error) {
      console.error('❌ 加载任务失败:', error);
    }
  }

  private async saveTasks(tasks?: IExecutableTask[]): Promise<void> {
    try {
      const stateRepo = this.container.get<SimpleStateRepository>(TYPES.StateRepository);
      const tasksToSave = tasks || Array.from(this.tasks.values());
      await stateRepo.set('executableTasks', tasksToSave);
    } catch (error) {
      console.error('❌ 保存任务失败:', error);
    }
  }

  private generateId(): string {
    return `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateProcessId(): string {
    return `proc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupExpress() {
    this.app = express();
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.static('public'));

    // CORS支持
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key');
      next();
    });
  }

  private setupRoutes() {
    // 主页 - 服务于构建好的React应用
    this.app.get('/', (req, res) => {
      const indexPath = path.join(__dirname, '../../../dist/renderer/index.html');
      console.log('尝试加载文件:', indexPath);
      res.sendFile(indexPath, (err) => {
        if (err) {
          console.log('文件加载失败:', err.message);
          // 如果构建的文件不存在，返回一个简单的HTML页面
          res.send(this.getFallbackHTML());
        } else {
          console.log('成功加载 React 应用');
        }
      });
    });

    // 静态文件服务 - 为构建的前端资源提供服务
    this.app.use('/assets', express.static(path.join(__dirname, '../../../dist/renderer/assets')));

    // 测试页面
    this.app.get('/test', (req, res) => {
      const fs = require('fs');
      try {
        const testHTML = fs.readFileSync('test-web-functions.html', 'utf-8');
        res.send(testHTML);
      } catch (error) {
        res.status(404).send('测试页面未找到');
      }
    });

    // 测试页面
    this.app.get('/test', (req, res) => {
      const fs = require('fs');
      try {
        const testHTML = fs.readFileSync('test-simple.html', 'utf-8');
        res.send(testHTML);
      } catch (error) {
        res.status(404).send('测试页面未找到');
      }
    });

    // 项目管理API
    this.app.get('/api/projects', async (req, res) => {
      try {
        const projects = this.projectService.getProjects();
        res.json({ success: true, data: projects });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/projects/scan', async (req, res) => {
      try {
        const { configId, path: scanPath, recursive = true } = req.body;

        let scanConfig;
        if (configId) {
          scanConfig = this.configService.getScanConfig(configId);
          if (!scanConfig) {
            return res.status(404).json({ success: false, error: '扫描配置不存在' });
          }
        } else {
          scanConfig = this.configService.getDefaultScanConfig();
        }

        const pathsToScan = scanPath ? [scanPath] : (scanConfig?.paths || ['.']);
        let allProjects: any[] = [];

        for (const path of pathsToScan) {
          const projects = await this.projectService.scanDirectory(path);
          allProjects = allProjects.concat(projects);
        }

        res.json({ success: true, data: allProjects, config: scanConfig });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 文件管理API
    this.app.get('/api/files/tree', async (req, res) => {
      try {
        const { path: dirPath = '.', depth = 3 } = req.query;
        const tree = await this.fileManager.getDirectoryTree(dirPath as string, Number(depth));
        res.json({ success: true, data: tree });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.get('/api/files/content', async (req, res) => {
      try {
        const { path: filePath } = req.query;
        if (!filePath) {
          return res.status(400).json({ success: false, error: '文件路径不能为空' });
        }

        const content = await this.fileManager.getFileContent(filePath as string);
        res.json({ success: true, data: { content, path: filePath } });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.get('/api/files/search', async (req, res) => {
      try {
        const { path: rootPath = '.', pattern } = req.query;
        if (!pattern) {
          return res.status(400).json({ success: false, error: '搜索模式不能为空' });
        }

        const results = await this.fileManager.searchFiles(rootPath as string, pattern as string);
        res.json({ success: true, data: results });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 工作流管理API
    this.app.get('/api/workflows', async (req, res) => {
      try {
        const workflows = await this.workflowService.getWorkflowGroups();
        res.json({ success: true, data: workflows });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/workflows/:id/start', async (req, res) => {
      try {
        const { id } = req.params;
        const workflow = await this.workflowService.getWorkflowGroup(id);
        if (!workflow) {
          return res.status(404).json({ success: false, error: '工作流组不存在' });
        }

        const execution = await this.orchestrator.startWorkflowGroup(workflow);
        res.json({ success: true, data: execution });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/workflows/:id/stop', async (req, res) => {
      try {
        const { id } = req.params;
        const workflow = await this.workflowService.getWorkflowGroup(id);
        if (!workflow) {
          return res.status(404).json({ success: false, error: '工作流组不存在' });
        }

        // 这里应该调用停止工作流的方法
        // 由于orchestrator可能没有stopWorkflowGroup方法，我们模拟一个响应
        const result = {
          workflowId: id,
          status: 'stopped',
          stoppedAt: new Date(),
          message: `工作流 ${workflow.name} 已停止`
        };

        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // ============= 任务运行器API =============

    // 获取所有任务
    this.app.get('/api/tasks', async (req, res) => {
      try {
        const tasks = Array.from(this.tasks.values());
        res.json({ success: true, data: tasks });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 创建新任务
    this.app.post('/api/tasks', async (req, res) => {
      try {
        const { name, command, description, category, icon, defaultCwd, projectId } = req.body;

        if (!name || !command) {
          return res.status(400).json({ success: false, error: '任务名称和命令不能为空' });
        }

        const task: IExecutableTask = {
          id: this.generateId(),
          name,
          command,
          description,
          category: category || 'general',
          icon: icon || '⚙️',
          defaultCwd,
          projectId,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        this.tasks.set(task.id, task);
        await this.saveTasks();

        res.json({ success: true, data: task });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 更新任务
    this.app.put('/api/tasks/:id', async (req, res) => {
      try {
        const { id } = req.params;
        const task = this.tasks.get(id);

        if (!task) {
          return res.status(404).json({ success: false, error: '任务不存在' });
        }

        const updatedTask: IExecutableTask = {
          ...task,
          ...req.body,
          id, // 确保ID不被修改
          updatedAt: new Date()
        };

        this.tasks.set(id, updatedTask);
        await this.saveTasks();

        res.json({ success: true, data: updatedTask });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 删除任务
    this.app.delete('/api/tasks/:id', async (req, res) => {
      try {
        const { id } = req.params;

        if (!this.tasks.has(id)) {
          return res.status(404).json({ success: false, error: '任务不存在' });
        }

        this.tasks.delete(id);
        await this.saveTasks();

        res.json({ success: true, message: '任务已删除' });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 运行任务
    this.app.post('/api/tasks/:id/run', async (req, res) => {
      try {
        const { id } = req.params;
        const { cwdOverride, projectId } = req.body;

        const task = this.tasks.get(id);
        if (!task) {
          return res.status(404).json({ success: false, error: '任务不存在' });
        }

        // 确定工作目录
        let cwd = cwdOverride || task.defaultCwd || process.cwd();

        // 如果指定了项目ID，使用项目路径
        if (projectId) {
          const projects = await this.projectService.getProjects();
          const project = projects.find(p => p.id === projectId);
          if (project) {
            cwd = project.path;
          }
        }

        // 创建进程句柄
        const processHandle: ProcessHandle = {
          id: this.generateProcessId(),
          taskId: id,
          taskName: task.name,
          command: task.command,
          cwd,
          status: 'running',
          startedAt: new Date(),
          output: []
        };

        // 启动进程
        const { spawn } = require('child_process');
        const [cmd, ...args] = task.command.split(' ');

        const childProcess = spawn(cmd, args, {
          cwd,
          stdio: ['pipe', 'pipe', 'pipe'],
          shell: true
        });

        processHandle.pid = childProcess.pid;
        this.runningProcesses.set(processHandle.id, processHandle);

        // 监听进程输出
        childProcess.stdout.on('data', (data: Buffer) => {
          const output = data.toString();
          processHandle.output.push(output);
          // 这里可以通过WebSocket发送实时输出
        });

        childProcess.stderr.on('data', (data: Buffer) => {
          const output = `ERROR: ${data.toString()}`;
          processHandle.output.push(output);
        });

        childProcess.on('close', (code: number) => {
          processHandle.status = code === 0 ? 'completed' : 'error';
          processHandle.endedAt = new Date();
        });

        childProcess.on('error', (error: Error) => {
          processHandle.status = 'error';
          processHandle.endedAt = new Date();
          processHandle.output.push(`ERROR: ${error.message}`);
        });

        res.json({ success: true, data: processHandle });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 获取正在运行的进程
    this.app.get('/api/processes', async (req, res) => {
      try {
        const processes = Array.from(this.runningProcesses.values());
        res.json({ success: true, data: processes });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 获取特定进程信息
    this.app.get('/api/processes/:id', async (req, res) => {
      try {
        const { id } = req.params;
        const process = this.runningProcesses.get(id);

        if (!process) {
          return res.status(404).json({ success: false, error: '进程不存在' });
        }

        res.json({ success: true, data: process });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 停止进程
    this.app.post('/api/processes/:id/stop', async (req, res) => {
      try {
        const { id } = req.params;
        const processHandle = this.runningProcesses.get(id);

        if (!processHandle) {
          return res.status(404).json({ success: false, error: '进程不存在' });
        }

        if (processHandle.pid) {
          try {
            process.kill(processHandle.pid, 'SIGTERM');
            processHandle.status = 'stopped';
            processHandle.endedAt = new Date();
            processHandle.output.push('进程已被用户停止');
          } catch (killError) {
            processHandle.status = 'error';
            processHandle.endedAt = new Date();
            processHandle.output.push(`停止进程失败: ${killError}`);
          }
        }

        res.json({ success: true, data: processHandle });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // ============= 终端执行API =============

    // 执行终端命令
    this.app.post('/api/terminal/execute', async (req, res) => {
      try {
        const { command, cwd } = req.body;

        if (!command) {
          return res.status(400).json({ success: false, error: '命令不能为空' });
        }

        const workingDir = cwd || process.cwd();

        // 使用child_process执行命令
        const { exec } = require('child_process');
        const { promisify } = require('util');
        const execAsync = promisify(exec);

        try {
          const { stdout, stderr } = await execAsync(command, {
            cwd: workingDir,
            timeout: 30000, // 30秒超时
            maxBuffer: 1024 * 1024 // 1MB缓冲区
          });

          res.json({
            success: true,
            data: {
              stdout: stdout || '',
              stderr: stderr || '',
              exitCode: 0,
              command,
              cwd: workingDir
            }
          });
        } catch (execError: any) {
          // 命令执行失败
          res.json({
            success: true,
            data: {
              stdout: execError.stdout || '',
              stderr: execError.stderr || execError.message,
              exitCode: execError.code || 1,
              command,
              cwd: workingDir
            }
          });
        }
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 批量操作API
    this.app.post('/api/batch/:operation', async (req, res) => {
      try {
        const { operation } = req.params;
        const { projectIds } = req.body;

        if (!projectIds || !Array.isArray(projectIds)) {
          return res.status(400).json({ success: false, error: '项目ID列表不能为空' });
        }

        let result;
        switch (operation) {
          case 'git-pull':
            result = await this.batchOps.batchGitPull(projectIds);
            break;
          case 'git-status':
            result = await this.batchOps.batchGitStatus(projectIds);
            break;
          case 'open-vscode':
            result = await this.batchOps.batchOpenInVSCode(projectIds);
            break;
          case 'open-terminal':
            result = await this.batchOps.batchOpenInTerminal(projectIds);
            break;
          default:
            return res.status(400).json({ success: false, error: '未知操作' });
        }

        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 系统状态API
    this.app.get('/api/status', (req, res) => {
      const projects = this.projectService.getProjects();
      const executions = this.orchestrator.getActiveExecutions();

      res.json({
        success: true,
        data: {
          projectCount: projects.length,
          activeWorkflows: executions.length,
          systemStatus: 'running',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: '2.0.0'
        }
      });
    });

    // n8n集成API
    this.app.get('/api/n8n/projects', async (req, res) => {
      try {
        const projects = this.projectService.getProjects();
        const n8nFormat = projects.map(project => ({
          id: project.id,
          name: project.name,
          type: project.type,
          path: project.path,
          git: project.gitInfo ? {
            branch: project.gitInfo.branch,
            hasChanges: project.gitInfo.hasUncommittedChanges,
            remoteUrl: project.gitInfo.remoteUrl
          } : null,
          metadata: {
            lastModified: new Date(),
            size: 0
          }
        }));

        res.json({ success: true, data: n8nFormat });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 配置管理API
    this.app.get('/api/config', (req, res) => {
      try {
        const config = this.configService.getConfig();
        res.json({ success: true, data: config });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.get('/api/config/scan', (req, res) => {
      try {
        const configs = this.configService.getScanConfigs();
        res.json({ success: true, data: configs });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/config/scan', (req, res) => {
      try {
        const newConfig = this.configService.addScanConfig(req.body);
        res.json({ success: true, data: newConfig });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.put('/api/config/scan/:id', (req, res) => {
      try {
        const { id } = req.params;
        const updatedConfig = this.configService.updateScanConfig(id, req.body);
        res.json({ success: true, data: updatedConfig });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.delete('/api/config/scan/:id', (req, res) => {
      try {
        const { id } = req.params;
        const deleted = this.configService.deleteScanConfig(id);
        res.json({ success: true, data: { deleted } });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/config/scan/:id/set-default', (req, res) => {
      try {
        const { id } = req.params;
        this.configService.setDefaultScanConfig(id);
        res.json({ success: true, data: { message: '默认配置已设置' } });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/config/validate-paths', (req, res) => {
      try {
        const { paths } = req.body;
        const validation = this.configService.validatePaths(paths);
        const stats = this.configService.getPathStats(paths);
        res.json({ success: true, data: { validation, stats } });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/config/export', (req, res) => {
      try {
        const configJson = this.configService.exportConfig();
        res.json({ success: true, data: { config: configJson } });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/config/import', (req, res) => {
      try {
        const { config } = req.body;
        this.configService.importConfig(config);
        res.json({ success: true, data: { message: '配置导入成功' } });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/config/reset', (req, res) => {
      try {
        this.configService.resetToDefault();
        res.json({ success: true, data: { message: '配置已重置为默认值' } });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.get('/api/config/info', (req, res) => {
      try {
        const info = {
          configFilePath: this.configService.getConfigFilePath(),
          configDirectory: this.configService.getConfigDirectory(),
          configExists: this.configService.configFileExists(),
          commonDirectories: this.configService.getCommonDirectories()
        };
        res.json({ success: true, data: info });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/config/backup', (req, res) => {
      try {
        const backupPath = this.configService.backupConfig();
        res.json({ success: true, data: { backupPath, message: '配置备份成功' } });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/config/normalize-paths', (req, res) => {
      try {
        const { paths } = req.body;
        const normalizedPaths = this.configService.normalizePaths(paths);
        res.json({ success: true, data: { normalizedPaths } });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // 项目操作API
    this.app.post('/api/projects/:id/open', async (req, res) => {
      try {
        const { id } = req.params;
        const { action } = req.body; // 'vscode', 'terminal', 'explorer'

        const projects = this.projectService.getProjects();
        const project = projects.find(p => p.id === id);

        if (!project) {
          return res.status(404).json({ success: false, error: '项目不存在' });
        }

        const result = await this.openProject(project, action);
        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    // API文档
    this.app.get('/api/docs', (req, res) => {
      res.json({
        title: 'DevWorkbench Enhanced API',
        version: '2.0.0',
        description: 'Enhanced API for project management and n8n integration',
        baseUrl: `http://localhost:${this.port}/api`,
        endpoints: {
          projects: {
            'GET /api/projects': 'Get all projects',
            'POST /api/projects/scan': 'Scan for projects'
          },
          files: {
            'GET /api/files/tree': 'Get directory tree',
            'GET /api/files/content': 'Get file content',
            'GET /api/files/search': 'Search files'
          },
          workflows: {
            'GET /api/workflows': 'Get workflow groups',
            'POST /api/workflows/:id/start': 'Start workflow',
            'POST /api/workflows/:id/stop': 'Stop workflow'
          },
          tasks: {
            'GET /api/tasks': 'Get all executable tasks',
            'POST /api/tasks': 'Create new task',
            'PUT /api/tasks/:id': 'Update task',
            'DELETE /api/tasks/:id': 'Delete task',
            'POST /api/tasks/:id/run': 'Run task'
          },
          processes: {
            'GET /api/processes': 'Get running processes',
            'GET /api/processes/:id': 'Get process details',
            'POST /api/processes/:id/stop': 'Stop process'
          },
          terminal: {
            'POST /api/terminal/execute': 'Execute terminal command'
          },
          batch: {
            'POST /api/batch/:operation': 'Execute batch operation'
          },
          config: {
            'GET /api/config': 'Get app configuration',
            'GET /api/config/scan': 'Get scan configurations',
            'POST /api/config/scan': 'Create scan configuration',
            'PUT /api/config/scan/:id': 'Update scan configuration',
            'DELETE /api/config/scan/:id': 'Delete scan configuration',
            'POST /api/config/scan/:id/set-default': 'Set default scan config',
            'POST /api/config/validate-paths': 'Validate paths',
            'POST /api/config/normalize-paths': 'Normalize paths to absolute',
            'GET /api/config/info': 'Get config file info and common directories',
            'POST /api/config/backup': 'Backup configuration file',
            'POST /api/config/export': 'Export configuration',
            'POST /api/config/import': 'Import configuration',
            'POST /api/config/reset': 'Reset to default configuration'
          },
          n8n: {
            'GET /api/n8n/projects': 'Get projects in n8n format'
          }
        }
      });
    });
  }

  private getFallbackHTML(): string {
    // 当构建的React应用不存在时的后备HTML
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DevWorkbench Enhanced</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; }
    .container { max-width: 800px; margin: 0 auto; }
    .status { background: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0; }
    .api-list { background: #f9f9f9; padding: 20px; border-radius: 8px; }
    .api-list a { display: block; margin: 8px 0; color: #0066cc; text-decoration: none; }
    .api-list a:hover { text-decoration: underline; }
  </style>
</head>
<body>
  <div class="container">
    <h1>🚀 DevWorkbench Enhanced</h1>
    <div class="status">
      <h3>⚠️ 前端应用未构建</h3>
      <p>React前端应用尚未构建。请运行以下命令构建前端：</p>
      <code>npm run build</code>
    </div>
    <div class="api-list">
      <h3>📡 可用的API端点：</h3>
      <a href="/api/docs">API文档</a>
      <a href="/api/projects">项目列表</a>
      <a href="/api/status">系统状态</a>
      <a href="/api/n8n/projects">n8n格式项目数据</a>
    </div>
  </div>
</body>
</html>`;
  }

  private async openProject(project: any, action: string): Promise<any> {
    const { spawn } = require('child_process');
    const path = require('path');

    try {
      switch (action) {
        case 'vscode':
          // 尝试多种VS Code命令
          const vscodeCommands = ['code', 'code.exe', 'Code.exe'];
          for (const cmd of vscodeCommands) {
            try {
              const process = spawn(cmd, [project.path], {
                detached: true,
                stdio: 'ignore',
                shell: true
              });
              process.unref();
              return { message: `VS Code已打开项目: ${project.name}`, command: cmd };
            } catch (error) {
              continue;
            }
          }
          throw new Error('未找到VS Code，请确保VS Code已安装并添加到PATH');

        case 'terminal':
          // Windows终端命令
          if (process.platform === 'win32') {
            // 尝试Windows Terminal, PowerShell, CMD
            const terminalCommands = [
              ['wt', '-d', project.path],
              ['powershell', '-Command', `cd '${project.path}'; powershell`],
              ['cmd', '/k', `cd /d "${project.path}"`]
            ];

            for (const [cmd, ...args] of terminalCommands) {
              try {
                const process = spawn(cmd, args, {
                  detached: true,
                  stdio: 'ignore',
                  shell: true
                });
                process.unref();
                return { message: `终端已打开项目: ${project.name}`, command: cmd };
              } catch (error) {
                continue;
              }
            }
          } else {
            // Linux/macOS终端命令
            const terminalCommands = [
              ['gnome-terminal', '--working-directory', project.path],
              ['xterm', '-e', `cd '${project.path}' && bash`],
              ['open', '-a', 'Terminal', project.path] // macOS
            ];

            for (const [cmd, ...args] of terminalCommands) {
              try {
                const process = spawn(cmd, args, {
                  detached: true,
                  stdio: 'ignore'
                });
                process.unref();
                return { message: `终端已打开项目: ${project.name}`, command: cmd };
              } catch (error) {
                continue;
              }
            }
          }
          throw new Error('未找到可用的终端程序');

        case 'explorer':
          // 文件管理器
          if (process.platform === 'win32') {
            spawn('explorer', [project.path], { detached: true, stdio: 'ignore' });
            return { message: `文件管理器已打开项目: ${project.name}` };
          } else if (process.platform === 'darwin') {
            spawn('open', [project.path], { detached: true, stdio: 'ignore' });
            return { message: `Finder已打开项目: ${project.name}` };
          } else {
            // Linux
            const fileManagers = ['nautilus', 'dolphin', 'thunar', 'pcmanfm'];
            for (const fm of fileManagers) {
              try {
                spawn(fm, [project.path], { detached: true, stdio: 'ignore' });
                return { message: `文件管理器已打开项目: ${project.name}`, command: fm };
              } catch (error) {
                continue;
              }
            }
            throw new Error('未找到可用的文件管理器');
          }

        default:
          throw new Error('未知操作类型');
      }
    } catch (error) {
      throw new Error(`打开失败: ${(error as Error).message}`);
    }
  }

  async start() {
    // 检查端口是否可用
    const portMonitor = this.container.get<PortMonitor>(TYPES.PortMonitor);
    const isPortAvailable = await portMonitor.checkPort(this.port);

    if (!isPortAvailable) {
      console.log(`⚠️  端口 ${this.port} 被占用，尝试使用端口 ${this.port + 1}`);
      this.port = this.port + 1;
    }

    this.server = this.app.listen(this.port, () => {
      console.log(`
🚀 DevWorkbench Enhanced Web Server 启动成功！

📍 访问地址: http://localhost:${this.port}
🔧 API端点: http://localhost:${this.port}/api
📚 API文档: http://localhost:${this.port}/api/docs
🔗 n8n集成: http://localhost:${this.port}/api/n8n/projects

✨ 增强功能:
📦 项目管理 - 多选、批量操作
📁 文件浏览器 - 树形结构、文件预览
🔄 工作流管理 - 可视化编辑
📊 批量操作 - 并发执行
🔍 实时监控 - 状态监控
🔗 n8n集成 - Webhook支持

按 Ctrl+C 停止服务器
`);
    });

    // 优雅关闭
    process.on('SIGINT', () => {
      console.log('\\n🛑 正在关闭服务器...');
      this.server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
      });
    });
  }
}

// 运行增强版Web服务器
if (require.main === module) {
  const webServer = new DevWorkbenchEnhancedWebServer();
  webServer.start().catch(console.error);
}

export { DevWorkbenchEnhancedWebServer };
