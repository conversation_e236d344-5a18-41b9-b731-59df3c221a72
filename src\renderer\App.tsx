/**
 * 主应用组件
 */

import React, { useEffect } from 'react';
import './styles/App.css';

const App: React.FC = () => {
  useEffect(() => {
    // 初始化应用
    console.log('DevWorkbench 应用已启动');
  }, []);

  return (
    <div className="app-layout">
      <header className="app-header">
        <div className="app-title">
          <h1>🚀 DevWorkbench</h1>
          <span className="app-subtitle">开发工作台</span>
        </div>
      </header>
      <main className="app-content">
        <div className="welcome-section">
          <h2>✅ 项目重构完成！</h2>
          <p>恭喜！您的 DevWorkbench 项目已经成功重构并现代化。</p>

          <div className="features-grid">
            <div className="feature-card">
              <h3>🏗️ 现代化构建</h3>
              <p>使用 Vite + TypeScript 的现代化构建流程</p>
            </div>

            <div className="feature-card">
              <h3>📁 清晰目录</h3>
              <p>重新组织的项目结构，代码更易维护</p>
            </div>

            <div className="feature-card">
              <h3>🔧 完整工具链</h3>
              <p>包含 ESLint、TypeScript 等完整开发工具</p>
            </div>

            <div className="feature-card">
              <h3>🌐 Web + Electron</h3>
              <p>支持 Web 服务器和 Electron 桌面应用</p>
            </div>
          </div>

          <div className="next-steps">
            <h3>🎯 下一步</h3>
            <ul>
              <li>运行 <code>npm run web</code> 启动 Web 服务器</li>
              <li>运行 <code>npm run dev</code> 启动开发模式</li>
              <li>运行 <code>npm run build</code> 构建生产版本</li>
              <li>运行 <code>npm run test-all</code> 执行所有测试</li>
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
};

export default App;
