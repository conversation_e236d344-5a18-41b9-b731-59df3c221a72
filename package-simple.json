{"name": "dev-workbench-simple", "version": "0.1.0", "description": "DevWorkbench 简化测试版本", "main": "dist/main/index.js", "scripts": {"test": "echo \"Test passed\"", "build:main": "tsc -p tsconfig.main.json", "start": "node dist/main/index.js"}, "dependencies": {"electron-store": "^8.1.0", "eventemitter2": "^6.4.9", "inversify": "^6.0.2", "reflect-metadata": "^0.2.2", "simple-git": "^3.20.0", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.9.0", "typescript": "^5.2.2"}}