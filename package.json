{"name": "dev-workbench-simple", "version": "0.1.0", "description": "DevWorkbench 简化测试版本", "main": "dist/main/index.js", "scripts": {"test": "echo \"Test passed\"", "build:main": "tsc -p tsconfig.main.json", "start": "node dist/main/index.js", "cli": "npx tsc cli.ts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && node cli.js", "interactive": "npx tsc interactive.ts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && node interactive.js", "web": "npx tsc web-server.ts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && node web-server.js", "web-enhanced": "npx tsc web-enhanced.ts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && node web-enhanced.js", "demo": "node demo.js", "test-all": "node test-core.js && node test-project-discovery.js && node test-process-management.js && node test-port-monitoring.js && node test-workflow-management.js && node integration-test.js"}, "dependencies": {"@types/express": "^5.0.3", "electron-store": "^8.1.0", "eventemitter2": "^6.4.9", "express": "^5.1.0", "inversify": "^6.0.2", "reflect-metadata": "^0.2.2", "simple-git": "^3.20.0", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.9.0", "typescript": "^5.2.2"}}