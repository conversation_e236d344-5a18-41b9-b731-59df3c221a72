/**
 * 主应用组件
 */

import React, { useEffect, useState } from 'react';
import './styles/App.css';

interface Project {
  name: string;
  path: string;
  status: string;
}

const App: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    // 初始化应用
    console.log('DevWorkbench 应用已启动');

    // 更新时间
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // 模拟加载项目数据
    setTimeout(() => {
      setProjects([
        { name: 'DevWorkbench', path: '/current/project', status: '运行中' },
        { name: 'Example Project', path: '/example/path', status: '就绪' }
      ]);
      setLoading(false);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="app-layout">
      <header className="app-header">
        <div className="app-title">
          <h1>🚀 DevWorkbench</h1>
          <span className="app-subtitle">开发工作台 - 现代化版本</span>
        </div>
        <div className="app-time">
          {currentTime.toLocaleString()}
        </div>
      </header>
      <main className="app-content">
        <div className="welcome-section">
          <h2>🎉 DevWorkbench 正在运行！</h2>
          <p>这是一个完全功能的 React 应用，展示了现代化的开发工作台。</p>

          <div className="status-section">
            <h3>📊 系统状态</h3>
            <div className="status-grid">
              <div className="status-card">
                <h4>🟢 服务器状态</h4>
                <p>正常运行</p>
              </div>
              <div className="status-card">
                <h4>⚡ React 应用</h4>
                <p>已加载</p>
              </div>
              <div className="status-card">
                <h4>🔧 构建系统</h4>
                <p>Vite + TypeScript</p>
              </div>
            </div>
          </div>

          <div className="projects-section">
            <h3>📁 项目列表</h3>
            {loading ? (
              <p>加载中...</p>
            ) : (
              <div className="projects-list">
                {projects.map((project, index) => (
                  <div key={index} className="project-card">
                    <h4>{project.name}</h4>
                    <p>路径: {project.path}</p>
                    <span className={`status ${project.status === '运行中' ? 'running' : 'ready'}`}>
                      {project.status}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default App;
