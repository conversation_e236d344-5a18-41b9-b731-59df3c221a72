/**
 * 主应用样式
 */

.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-title h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
}

.app-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

.app-time {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-family: monospace;
}

.app-content {
  flex: 1;
  padding: 40px 24px;
  background: #f8fafc;
}

.welcome-section {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section h2 {
  font-size: 32px;
  color: #1a202c;
  margin-bottom: 16px;
  text-align: center;
}

.welcome-section>p {
  font-size: 18px;
  color: #4a5568;
  text-align: center;
  margin-bottom: 40px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.feature-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: transform 0.2s, box-shadow 0.2s;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-card h3 {
  font-size: 20px;
  color: #2d3748;
  margin-bottom: 12px;
}

.feature-card p {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

.next-steps {
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.next-steps h3 {
  font-size: 24px;
  color: #2d3748;
  margin-bottom: 20px;
}

.next-steps ul {
  list-style: none;
  padding: 0;
}

.next-steps li {
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
  font-size: 16px;
}

.next-steps li:last-child {
  border-bottom: none;
}

.next-steps code {
  background: #f7fafc;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  color: #e53e3e;
  font-weight: 600;
}

/* 新增样式 */
.status-section,
.projects-section {
  margin-top: 32px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.status-card {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.status-card h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #2d3748;
}

.status-card p {
  margin: 0;
  color: #4a5568;
  font-size: 14px;
}

.projects-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.project-card {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-card h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #2d3748;
}

.project-card p {
  margin: 0;
  color: #4a5568;
  font-size: 14px;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.status.running {
  background: #c6f6d5;
  color: #22543d;
}

.status.ready {
  background: #bee3f8;
  color: #2a4365;
}